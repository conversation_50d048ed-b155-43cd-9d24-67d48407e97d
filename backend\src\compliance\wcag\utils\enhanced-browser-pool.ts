/**
 * Enhanced Browser Pool with Intelligent Load Balancing and Health Monitoring
 * Extends BrowserPool with advanced features while maintaining backward compatibility
 */

import puppeteer from 'puppeteer';
import BrowserPool, { BrowserPoolConfig, BrowserInstance, PageInstance } from './browser-pool';
import logger from '../../../utils/logger';

export interface EnhancedBrowserPoolConfig extends BrowserPoolConfig {
  enableLoadBalancing: boolean;
  enablePredictiveSpawning: boolean;
  enableHealthMonitoring: boolean;
  enableAutoRecovery: boolean;
  healthCheckInterval: number;
  memoryThreshold: number;
  cpuThreshold: number;
  loadBalancingStrategy: 'round-robin' | 'least-loaded' | 'performance-based';
  predictiveSpawningThreshold: number;
  autoRecoveryMaxAttempts: number;
}

export interface WorkloadType {
  type: 'light' | 'medium' | 'heavy';
  estimatedDuration: number;
  resourceIntensive: boolean;
  priority: number;
}

export interface BrowserHealthMetrics {
  browserId: string;
  memoryUsageMB: number;
  cpuUsagePercent: number;
  pageCount: number;
  responseTime: number;
  errorCount: number;
  lastHealthCheck: Date;
  healthScore: number; // 0-100
  isHealthy: boolean;
}

export interface LoadBalancingMetrics {
  totalRequests: number;
  averageResponseTime: number;
  loadDistribution: Map<string, number>;
  optimalBrowserId: string;
  balancingEfficiency: number;
}

export interface EnhancedBrowserInstance extends BrowserInstance {
  browserId: string;
  healthMetrics: BrowserHealthMetrics;
  workloadHistory: WorkloadType[];
  performanceScore: number;
  recoveryAttempts: number;
}

/**
 * Enhanced BrowserPool with intelligent load balancing and predictive capabilities
 */
export class EnhancedBrowserPool {
  private static enhancedInstance: EnhancedBrowserPool;
  private baseBrowserPool: BrowserPool;
  private enhancedConfig: EnhancedBrowserPoolConfig;
  private enhancedBrowsers = new Map<string, EnhancedBrowserInstance>();
  private loadBalancingMetrics: LoadBalancingMetrics;
  private healthMonitorInterval: NodeJS.Timeout | null = null;
  private predictiveSpawningInterval: NodeJS.Timeout | null = null;
  private requestQueue: Array<{
    workload: WorkloadType;
    resolve: (page: PageInstance) => void;
    reject: (error: Error) => void;
  }> = [];

  private constructor(config?: Partial<EnhancedBrowserPoolConfig>) {
    const baseConfig = {
      maxBrowsers: config?.maxBrowsers || 4,
      maxPagesPerBrowser: config?.maxPagesPerBrowser || 3,
      browserTimeout: config?.browserTimeout || 300000,
      pageTimeout: config?.pageTimeout || 120000,
      enableMemoryMonitoring: config?.enableMemoryMonitoring ?? true,
      memoryThresholdMB: config?.memoryThresholdMB || 512,
    };

    this.baseBrowserPool = BrowserPool.getInstance(baseConfig);

    this.enhancedConfig = {
      ...baseConfig,
      enableLoadBalancing: config?.enableLoadBalancing ?? true,
      enablePredictiveSpawning: config?.enablePredictiveSpawning ?? true,
      enableHealthMonitoring: config?.enableHealthMonitoring ?? true,
      enableAutoRecovery: config?.enableAutoRecovery ?? true,
      healthCheckInterval: config?.healthCheckInterval || 30000, // 30 seconds
      memoryThreshold: config?.memoryThreshold || 400, // MB
      cpuThreshold: config?.cpuThreshold || 80, // Percentage
      loadBalancingStrategy: config?.loadBalancingStrategy || 'performance-based',
      predictiveSpawningThreshold: config?.predictiveSpawningThreshold || 0.8, // 80% capacity
      autoRecoveryMaxAttempts: config?.autoRecoveryMaxAttempts || 3,
    };

    this.loadBalancingMetrics = {
      totalRequests: 0,
      averageResponseTime: 0,
      loadDistribution: new Map(),
      optimalBrowserId: '',
      balancingEfficiency: 0,
    };

    this.initializeEnhancedFeatures();

    logger.info('🚀 Enhanced Browser Pool initialized', {
      loadBalancing: this.enhancedConfig.enableLoadBalancing,
      healthMonitoring: this.enhancedConfig.enableHealthMonitoring,
      predictiveSpawning: this.enhancedConfig.enablePredictiveSpawning,
      autoRecovery: this.enhancedConfig.enableAutoRecovery,
    });
  }

  static getEnhancedInstance(config?: Partial<EnhancedBrowserPoolConfig>): EnhancedBrowserPool {
    if (!EnhancedBrowserPool.enhancedInstance) {
      EnhancedBrowserPool.enhancedInstance = new EnhancedBrowserPool(config);
    }
    return EnhancedBrowserPool.enhancedInstance;
  }

  /**
   * Get optimal browser based on workload type and current load
   */
  async getOptimalPage(workload: WorkloadType, scanId: string): Promise<PageInstance> {
    const startTime = Date.now();

    if (this.enhancedConfig.enableLoadBalancing) {
      const optimalBrowser = await this.selectOptimalBrowser(workload);
      if (optimalBrowser) {
        const page = await this.createPageFromBrowser(optimalBrowser, scanId);
        this.updateLoadBalancingMetrics(optimalBrowser.browserId, Date.now() - startTime);
        return page;
      }
    }

    // Fallback to base implementation
    return this.getPage(scanId);
  }

  /**
   * Enhanced health monitoring for all browsers
   */
  async monitorBrowserHealth(): Promise<BrowserHealthMetrics[]> {
    const healthReports: BrowserHealthMetrics[] = [];

    for (const [browserId, browserInstance] of this.enhancedBrowsers) {
      const healthMetrics = await this.checkBrowserHealth(browserId, browserInstance);
      healthReports.push(healthMetrics);

      // Auto-recovery if browser is unhealthy
      if (this.enhancedConfig.enableAutoRecovery && !healthMetrics.isHealthy) {
        await this.attemptBrowserRecovery(browserId, browserInstance);
      }
    }

    return healthReports;
  }

  /**
   * Predictive browser spawning based on load patterns
   */
  async predictiveSpawning(): Promise<void> {
    if (!this.enhancedConfig.enablePredictiveSpawning) {
      return;
    }

    const currentLoad = this.calculateCurrentLoad();
    const predictedLoad = this.predictFutureLoad();

    if (predictedLoad > this.enhancedConfig.predictiveSpawningThreshold) {
      logger.info(
        `🔮 Predictive spawning triggered: current=${currentLoad.toFixed(2)}, predicted=${predictedLoad.toFixed(2)}`,
      );
      await this.preSpawnBrowser();
    }
  }

  /**
   * Get comprehensive pool analytics
   */
  getEnhancedStats(): {
    baseStats: ReturnType<BrowserPool['getStats']>;
    healthMetrics: BrowserHealthMetrics[];
    loadBalancing: LoadBalancingMetrics;
    predictiveAccuracy: number;
    autoRecoveryStats: {
      totalAttempts: number;
      successfulRecoveries: number;
      failedRecoveries: number;
    };
  } {
    const baseStats = this.baseBrowserPool.getStats();
    const healthMetrics = Array.from(this.enhancedBrowsers.values()).map((b) => b.healthMetrics);

    const totalRecoveryAttempts = Array.from(this.enhancedBrowsers.values()).reduce(
      (sum, b) => sum + b.recoveryAttempts,
      0,
    );

    const successfulRecoveries = Array.from(this.enhancedBrowsers.values()).filter(
      (b) => b.healthMetrics.isHealthy && b.recoveryAttempts > 0,
    ).length;

    return {
      baseStats,
      healthMetrics,
      loadBalancing: this.loadBalancingMetrics,
      predictiveAccuracy: this.calculatePredictiveAccuracy(),
      autoRecoveryStats: {
        totalAttempts: totalRecoveryAttempts,
        successfulRecoveries,
        failedRecoveries: totalRecoveryAttempts - successfulRecoveries,
      },
    };
  }

  /**
   * Private helper methods
   */
  private initializeEnhancedFeatures(): void {
    if (this.enhancedConfig.enableHealthMonitoring) {
      this.startHealthMonitoring();
    }

    if (this.enhancedConfig.enablePredictiveSpawning) {
      this.startPredictiveSpawning();
    }
  }

  private startHealthMonitoring(): void {
    this.healthMonitorInterval = setInterval(async () => {
      try {
        await this.monitorBrowserHealth();
      } catch (error) {
        logger.error('❌ Health monitoring failed:', error as Record<string, unknown>);
      }
    }, this.enhancedConfig.healthCheckInterval);
  }

  private startPredictiveSpawning(): void {
    this.predictiveSpawningInterval = setInterval(async () => {
      try {
        await this.predictiveSpawning();
      } catch (error) {
        logger.error('❌ Predictive spawning failed:', error as Record<string, unknown>);
      }
    }, 60000); // Check every minute
  }

  private async selectOptimalBrowser(
    workload: WorkloadType,
  ): Promise<EnhancedBrowserInstance | null> {
    const availableBrowsers = Array.from(this.enhancedBrowsers.values()).filter(
      (b) => b.healthMetrics.isHealthy && b.pages.length < this.enhancedConfig.maxPagesPerBrowser!,
    );

    if (availableBrowsers.length === 0) {
      return null;
    }

    switch (this.enhancedConfig.loadBalancingStrategy) {
      case 'round-robin':
        return this.selectRoundRobin(availableBrowsers);
      case 'least-loaded':
        return this.selectLeastLoaded(availableBrowsers);
      case 'performance-based':
        return this.selectPerformanceBased(availableBrowsers, workload);
      default:
        return availableBrowsers[0];
    }
  }

  private selectRoundRobin(browsers: EnhancedBrowserInstance[]): EnhancedBrowserInstance {
    // Simple round-robin selection
    const sortedByLastUsed = browsers.sort((a, b) => a.lastUsed.getTime() - b.lastUsed.getTime());
    return sortedByLastUsed[0];
  }

  private selectLeastLoaded(browsers: EnhancedBrowserInstance[]): EnhancedBrowserInstance {
    return browsers.reduce((least, current) =>
      current.pages.length < least.pages.length ? current : least,
    );
  }

  private selectPerformanceBased(
    browsers: EnhancedBrowserInstance[],
    workload: WorkloadType,
  ): EnhancedBrowserInstance {
    // Score browsers based on performance metrics and workload compatibility
    const scoredBrowsers = browsers.map((browser) => ({
      browser,
      score: this.calculateBrowserScore(browser, workload),
    }));

    scoredBrowsers.sort((a, b) => b.score - a.score);
    return scoredBrowsers[0].browser;
  }

  private calculateBrowserScore(browser: EnhancedBrowserInstance, workload: WorkloadType): number {
    const healthScore = browser.healthMetrics.healthScore;
    const loadScore = (1 - browser.pages.length / this.enhancedConfig.maxPagesPerBrowser!) * 100;
    const performanceScore = browser.performanceScore;

    // Weight scores based on workload type
    const weights =
      workload.type === 'heavy'
        ? { health: 0.4, load: 0.3, performance: 0.3 }
        : { health: 0.3, load: 0.4, performance: 0.3 };

    return (
      healthScore * weights.health +
      loadScore * weights.load +
      performanceScore * weights.performance
    );
  }

  private async checkBrowserHealth(
    browserId: string,
    browserInstance: EnhancedBrowserInstance,
  ): Promise<BrowserHealthMetrics> {
    const startTime = Date.now();
    let isHealthy = true;
    let responseTime = 0;
    let errorCount = 0;

    try {
      // Test browser responsiveness
      const testPage = await browserInstance.browser.newPage();
      await testPage.goto('data:text/html,<html><body>Health Check</body></html>');
      responseTime = Date.now() - startTime;
      await testPage.close();
    } catch (error) {
      isHealthy = false;
      errorCount++;
      responseTime = Date.now() - startTime;
    }

    // Get memory usage (simplified)
    const memoryUsageMB = process.memoryUsage().heapUsed / 1024 / 1024;
    const cpuUsagePercent = 0; // Would need additional monitoring for real CPU usage

    // Calculate health score
    const healthScore = this.calculateHealthScore({
      responseTime,
      memoryUsageMB,
      cpuUsagePercent,
      errorCount,
      pageCount: browserInstance.pages.length,
    });

    const healthMetrics: BrowserHealthMetrics = {
      browserId,
      memoryUsageMB,
      cpuUsagePercent,
      pageCount: browserInstance.pages.length,
      responseTime,
      errorCount,
      lastHealthCheck: new Date(),
      healthScore,
      isHealthy: isHealthy && healthScore > 60,
    };

    // Update browser instance
    browserInstance.healthMetrics = healthMetrics;
    browserInstance.isHealthy = healthMetrics.isHealthy;

    return healthMetrics;
  }

  private calculateHealthScore(metrics: {
    responseTime: number;
    memoryUsageMB: number;
    cpuUsagePercent: number;
    errorCount: number;
    pageCount: number;
  }): number {
    // Response time score (0-30 points)
    const responseScore = Math.max(0, 30 - metrics.responseTime / 100);

    // Memory usage score (0-25 points)
    const memoryScore = Math.max(
      0,
      25 - (metrics.memoryUsageMB / this.enhancedConfig.memoryThreshold) * 25,
    );

    // CPU usage score (0-25 points)
    const cpuScore = Math.max(
      0,
      25 - (metrics.cpuUsagePercent / this.enhancedConfig.cpuThreshold) * 25,
    );

    // Error count score (0-20 points)
    const errorScore = Math.max(0, 20 - metrics.errorCount * 5);

    return Math.min(100, responseScore + memoryScore + cpuScore + errorScore);
  }

  private async attemptBrowserRecovery(
    browserId: string,
    browserInstance: EnhancedBrowserInstance,
  ): Promise<void> {
    if (browserInstance.recoveryAttempts >= this.enhancedConfig.autoRecoveryMaxAttempts) {
      logger.warn(`🚨 Browser ${browserId} exceeded max recovery attempts, removing from pool`);
      await this.removeBrowser(browserId);
      return;
    }

    logger.info(
      `🔧 Attempting recovery for browser ${browserId} (attempt ${browserInstance.recoveryAttempts + 1})`,
    );
    browserInstance.recoveryAttempts++;

    try {
      // Close all pages in the browser
      for (const page of browserInstance.pages) {
        try {
          await page.close();
        } catch (error) {
          logger.warn(`Error closing page during recovery:`, error as Record<string, unknown>);
        }
      }
      browserInstance.pages = [];

      // Test browser with a simple page
      const testPage = await browserInstance.browser.newPage();
      await testPage.goto('data:text/html,<html><body>Recovery Test</body></html>');
      await testPage.close();

      // Reset health metrics
      browserInstance.healthMetrics.isHealthy = true;
      browserInstance.healthMetrics.errorCount = 0;
      browserInstance.isHealthy = true;

      logger.info(`✅ Browser ${browserId} recovery successful`);
    } catch (error) {
      logger.error(`❌ Browser ${browserId} recovery failed:`, error as Record<string, unknown>);
      browserInstance.healthMetrics.isHealthy = false;
      browserInstance.isHealthy = false;
    }
  }

  private async removeBrowser(browserId: string): Promise<void> {
    const browserInstance = this.enhancedBrowsers.get(browserId);
    if (browserInstance) {
      try {
        await browserInstance.browser.close();
      } catch (error) {
        logger.warn(`Error closing browser during removal:`, error);
      }
      this.enhancedBrowsers.delete(browserId);
    }
  }

  private calculateCurrentLoad(): number {
    const totalCapacity = this.enhancedBrowsers.size * this.enhancedConfig.maxPagesPerBrowser!;
    const currentUsage = Array.from(this.enhancedBrowsers.values()).reduce(
      (sum, browser) => sum + browser.pages.length,
      0,
    );

    return totalCapacity > 0 ? currentUsage / totalCapacity : 0;
  }

  private predictFutureLoad(): number {
    // Simple prediction based on recent request patterns
    // In a real implementation, this would use more sophisticated algorithms
    const recentRequests = this.loadBalancingMetrics.totalRequests;
    const currentLoad = this.calculateCurrentLoad();

    // Predict 20% increase if recent activity is high
    return recentRequests > 10 ? currentLoad * 1.2 : currentLoad;
  }

  private async preSpawnBrowser(): Promise<void> {
    if (this.enhancedBrowsers.size < this.enhancedConfig.maxBrowsers!) {
      logger.info('🔮 Pre-spawning browser for predicted load');
      await this.createEnhancedBrowser();
    }
  }

  private async createEnhancedBrowser(): Promise<EnhancedBrowserInstance> {
    const browser = await puppeteer.launch({
      headless: true,
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-accelerated-2d-canvas',
        '--no-first-run',
        '--no-zygote',
        '--disable-gpu',
        '--memory-pressure-off',
        '--max_old_space_size=4096',
      ],
    });

    const browserId = `enhanced-browser-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    const enhancedInstance: EnhancedBrowserInstance = {
      browserId,
      browser,
      pages: [],
      createdAt: new Date(),
      lastUsed: new Date(),
      memoryUsage: 0,
      isHealthy: true,
      healthMetrics: {
        browserId,
        memoryUsageMB: 0,
        cpuUsagePercent: 0,
        pageCount: 0,
        responseTime: 0,
        errorCount: 0,
        lastHealthCheck: new Date(),
        healthScore: 100,
        isHealthy: true,
      },
      workloadHistory: [],
      performanceScore: 100,
      recoveryAttempts: 0,
    };

    this.enhancedBrowsers.set(browserId, enhancedInstance);
    return enhancedInstance;
  }

  private async createPageFromBrowser(
    browserInstance: EnhancedBrowserInstance,
    _scanId: string,
  ): Promise<PageInstance> {
    const page = await browserInstance.browser.newPage();

    // Configure page (simplified)
    await page.setViewport({ width: 1920, height: 1080 });

    const pageInstance: PageInstance = {
      page,
      browser: browserInstance.browser,
      isInUse: true,
      createdAt: new Date(),
      lastUsed: new Date(),
      scanCount: 1,
    };

    browserInstance.pages.push(page);
    browserInstance.lastUsed = new Date();

    return pageInstance;
  }

  private updateLoadBalancingMetrics(browserId: string, responseTime: number): void {
    this.loadBalancingMetrics.totalRequests++;

    // Update average response time
    const totalTime =
      this.loadBalancingMetrics.averageResponseTime * (this.loadBalancingMetrics.totalRequests - 1);
    this.loadBalancingMetrics.averageResponseTime =
      (totalTime + responseTime) / this.loadBalancingMetrics.totalRequests;

    // Update load distribution
    const currentCount = this.loadBalancingMetrics.loadDistribution.get(browserId) || 0;
    this.loadBalancingMetrics.loadDistribution.set(browserId, currentCount + 1);

    // Update optimal browser
    this.loadBalancingMetrics.optimalBrowserId = browserId;

    // Calculate balancing efficiency (simplified)
    const distribution = Array.from(this.loadBalancingMetrics.loadDistribution.values());
    const variance = this.calculateVariance(distribution);
    this.loadBalancingMetrics.balancingEfficiency = Math.max(0, 100 - variance);
  }

  private calculateVariance(values: number[]): number {
    if (values.length === 0) return 0;
    const mean = values.reduce((sum, val) => sum + val, 0) / values.length;
    const squaredDiffs = values.map((val) => Math.pow(val - mean, 2));
    return squaredDiffs.reduce((sum, val) => sum + val, 0) / values.length;
  }

  private calculatePredictiveAccuracy(): number {
    // Simplified predictive accuracy calculation
    // In a real implementation, this would track prediction vs actual outcomes
    return 85; // Placeholder value
  }

  /**
   * Backward compatibility methods
   */
  async getPage(scanId: string): Promise<PageInstance> {
    // Default workload for backward compatibility
    const defaultWorkload: WorkloadType = {
      type: 'medium',
      estimatedDuration: 30000,
      resourceIntensive: false,
      priority: 1,
    };

    return this.getOptimalPage(defaultWorkload, scanId);
  }

  /**
   * Enhanced shutdown with cleanup of enhanced features
   */
  async shutdown(): Promise<void> {
    logger.info('🔄 Shutting down enhanced browser pool');

    // Clear enhanced intervals
    if (this.healthMonitorInterval) {
      clearInterval(this.healthMonitorInterval);
    }
    if (this.predictiveSpawningInterval) {
      clearInterval(this.predictiveSpawningInterval);
    }

    // Close enhanced browsers
    for (const browserInstance of this.enhancedBrowsers.values()) {
      try {
        await browserInstance.browser.close();
      } catch (error) {
        logger.warn('Error closing enhanced browser during shutdown:', error as Record<string, unknown>);
      }
    }

    this.enhancedBrowsers.clear();

    // Call parent shutdown
    await this.baseBrowserPool.shutdown();

    logger.info('✅ Enhanced browser pool shutdown completed');
  }
}

export default EnhancedBrowserPool;
