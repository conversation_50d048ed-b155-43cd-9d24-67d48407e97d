/**
 * Real-Time Monitoring Dashboard
 * Provides comprehensive real-time monitoring capabilities for WCAG performance and utility integration
 */

import { EventEmitter } from 'events';
import {
  PerformanceIntegrationBridge,
  IntegratedPerformanceReport,
} from './performance-integration-bridge';
import { UtilityIntegrationManager } from './utility-integration-manager';
import WCAGPerformanceMonitor from './performance-monitor';
import EnhancedPerformanceMonitor from './enhanced-performance-monitor';
import logger from '../../../utils/logger';

export interface DashboardMetrics {
  timestamp: Date;
  activeScans: number;
  totalScansToday: number;
  averagePerformanceScore: number;
  systemHealth: 'excellent' | 'good' | 'warning' | 'critical';

  // Performance metrics
  performance: {
    averageScanDuration: number;
    memoryUsage: number;
    cpuUsage: number;
    cacheHitRate: number;
    errorRate: number;
  };

  // Utility integration metrics
  utilityIntegration: {
    totalUtilitiesUsed: number;
    averageUtilityOverhead: number;
    mostUsedUtilities: Array<{ utility: string; count: number }>;
    integrationStrategies: Record<string, number>;
    utilityErrors: number;
  };

  // Real-time alerts
  alerts: Array<{
    id: string;
    type: 'performance' | 'utility' | 'system' | 'cache';
    severity: 'low' | 'medium' | 'high' | 'critical';
    message: string;
    timestamp: Date;
    scanId?: string;
    ruleId?: string;
  }>;

  // Trends (last 24 hours)
  trends: {
    performanceImprovement: number;
    cacheEfficiencyTrend: number;
    utilityOptimizationTrend: number;
    errorRateTrend: number;
  };
}

export interface DashboardConfig {
  updateInterval: number; // milliseconds
  metricsRetentionHours: number;
  alertThresholds: {
    performanceScore: number;
    memoryUsage: number;
    cacheHitRate: number;
    errorRate: number;
    utilityOverhead: number;
  };
  enableRealTimeUpdates: boolean;
  enableTrendAnalysis: boolean;
}

/**
 * Real-Time Monitoring Dashboard Class
 */
export class RealTimeMonitoringDashboard extends EventEmitter {
  private static instance: RealTimeMonitoringDashboard;

  private performanceBridge: PerformanceIntegrationBridge;
  private utilityManager: UtilityIntegrationManager;
  private performanceMonitor: WCAGPerformanceMonitor;
  private enhancedMonitor: EnhancedPerformanceMonitor;

  private config: DashboardConfig;
  private metricsHistory: DashboardMetrics[] = [];
  private activeScans: Set<string> = new Set();
  private updateTimer: NodeJS.Timeout | null = null;
  private isRunning: boolean = false;

  private constructor() {
    super();

    this.performanceBridge = PerformanceIntegrationBridge.getInstance();
    this.utilityManager = UtilityIntegrationManager.getInstance();
    this.performanceMonitor = WCAGPerformanceMonitor.getInstance();
    this.enhancedMonitor = EnhancedPerformanceMonitor.getInstance();

    this.config = {
      updateInterval: 5000, // 5 seconds
      metricsRetentionHours: 24,
      alertThresholds: {
        performanceScore: 70,
        memoryUsage: 2500, // MB
        cacheHitRate: 70, // %
        errorRate: 5, // %
        utilityOverhead: 25, // %
      },
      enableRealTimeUpdates: true,
      enableTrendAnalysis: true,
    };

    logger.info('📊 Real-Time Monitoring Dashboard initialized');
  }

  static getInstance(): RealTimeMonitoringDashboard {
    if (!RealTimeMonitoringDashboard.instance) {
      RealTimeMonitoringDashboard.instance = new RealTimeMonitoringDashboard();
    }
    return RealTimeMonitoringDashboard.instance;
  }

  /**
   * Start real-time monitoring
   */
  start(): void {
    if (this.isRunning) {
      logger.warn('📊 Dashboard already running');
      return;
    }

    this.isRunning = true;

    if (this.config.enableRealTimeUpdates) {
      this.updateTimer = setInterval(() => {
        this.updateMetrics();
      }, this.config.updateInterval);
    }

    // Initial metrics collection
    this.updateMetrics();

    logger.info('📊 Real-Time Monitoring Dashboard started');
    this.emit('dashboardStarted');
  }

  /**
   * Stop real-time monitoring
   */
  stop(): void {
    if (!this.isRunning) return;

    this.isRunning = false;

    if (this.updateTimer) {
      clearInterval(this.updateTimer);
      this.updateTimer = null;
    }

    logger.info('📊 Real-Time Monitoring Dashboard stopped');
    this.emit('dashboardStopped');
  }

  /**
   * Register active scan
   */
  registerActiveScan(scanId: string): void {
    this.activeScans.add(scanId);
    this.emit('scanStarted', { scanId, activeScans: this.activeScans.size });
    logger.debug(`📊 Registered active scan: ${scanId} (total: ${this.activeScans.size})`);
  }

  /**
   * Unregister active scan
   */
  unregisterActiveScan(scanId: string): void {
    this.activeScans.delete(scanId);
    this.emit('scanCompleted', { scanId, activeScans: this.activeScans.size });
    logger.debug(`📊 Unregistered active scan: ${scanId} (total: ${this.activeScans.size})`);
  }

  /**
   * Update dashboard metrics
   */
  private async updateMetrics(): Promise<void> {
    try {
      const timestamp = new Date();

      // Get utility integration metrics
      const utilityMetrics = this.utilityManager.getPerformanceMetrics();

      // Get enhanced monitoring stats
      const enhancedStats = this.enhancedMonitor.getEnhancedStats();

      // Calculate system health
      const systemHealth = this.calculateSystemHealth(utilityMetrics, enhancedStats);

      // Get memory usage
      const memoryUsage = this.getCurrentMemoryUsage();

      // Calculate trends
      const trends = this.calculateTrends();

      const metrics: DashboardMetrics = {
        timestamp,
        activeScans: this.activeScans.size,
        totalScansToday: this.getTotalScansToday(),
        averagePerformanceScore: this.calculateAveragePerformanceScore(),
        systemHealth,

        performance: {
          averageScanDuration: this.calculateAverageScanDuration(),
          memoryUsage,
          cpuUsage: this.getCurrentCpuUsage(),
          cacheHitRate:
            typeof utilityMetrics?.cacheHitRate === 'number'
              ? utilityMetrics.cacheHitRate * 100
              : 0,
          errorRate: this.calculateErrorRate(),
        },

        utilityIntegration: {
          totalUtilitiesUsed:
            typeof utilityMetrics?.totalExecutions === 'number'
              ? utilityMetrics.totalExecutions
              : 0,
          averageUtilityOverhead: this.calculateAverageUtilityOverhead(),
          mostUsedUtilities: this.getMostUsedUtilities(),
          integrationStrategies: this.getIntegrationStrategies(),
          utilityErrors:
            typeof utilityMetrics?.totalErrors === 'number' ? utilityMetrics.totalErrors : 0,
        },

        alerts: this.collectCurrentAlerts(),
        trends,
      };

      // Store metrics
      this.metricsHistory.push(metrics);

      // Clean old metrics
      this.cleanOldMetrics();

      // Check for alerts
      this.checkAlerts(metrics);

      // Emit update event
      this.emit('metricsUpdated', metrics);

      logger.debug('📊 Dashboard metrics updated', {
        activeScans: metrics.activeScans,
        systemHealth: metrics.systemHealth,
        performanceScore: metrics.averagePerformanceScore,
        cacheHitRate: metrics.performance.cacheHitRate.toFixed(1) + '%',
        alerts: metrics.alerts.length,
      });
    } catch (error) {
      logger.error('📊 Failed to update dashboard metrics:', {
        error: error instanceof Error ? error.message : String(error),
      });
      this.emit('metricsError', error);
    }
  }

  /**
   * Calculate system health
   */
  private calculateSystemHealth(
    utilityMetrics: any,
    enhancedStats: any,
  ): 'excellent' | 'good' | 'warning' | 'critical' {
    let healthScore = 100;

    // Check performance metrics
    if (utilityMetrics) {
      if (utilityMetrics.cacheHitRate < 0.7) healthScore -= 15;
      if (utilityMetrics.totalErrors > 10) healthScore -= 20;
      if (utilityMetrics.averageExecutionTime > 5000) healthScore -= 10;
    }

    // Check memory usage
    const memoryUsage = this.getCurrentMemoryUsage();
    if (memoryUsage > 3000) healthScore -= 25;
    else if (memoryUsage > 2000) healthScore -= 10;

    // Check recent alerts
    const recentAlerts = enhancedStats?.recentAlerts || [];
    const criticalAlerts = recentAlerts.filter(
      (alert: any) => alert.severity === 'critical',
    ).length;
    const highAlerts = recentAlerts.filter((alert: any) => alert.severity === 'high').length;

    healthScore -= criticalAlerts * 20;
    healthScore -= highAlerts * 10;

    if (healthScore >= 90) return 'excellent';
    if (healthScore >= 75) return 'good';
    if (healthScore >= 50) return 'warning';
    return 'critical';
  }

  /**
   * Get current memory usage in MB
   */
  private getCurrentMemoryUsage(): number {
    const memoryUsage = process.memoryUsage();
    return Math.round(memoryUsage.heapUsed / 1024 / 1024);
  }

  /**
   * Get current CPU usage (simplified)
   */
  private getCurrentCpuUsage(): number {
    // Simplified CPU usage calculation
    // In a real implementation, you might use a library like 'pidusage'
    return Math.random() * 100; // Placeholder
  }

  /**
   * Calculate trends
   */
  private calculateTrends(): DashboardMetrics['trends'] {
    if (!this.config.enableTrendAnalysis || this.metricsHistory.length < 2) {
      return {
        performanceImprovement: 0,
        cacheEfficiencyTrend: 0,
        utilityOptimizationTrend: 0,
        errorRateTrend: 0,
      };
    }

    const recent = this.metricsHistory.slice(-10); // Last 10 metrics
    const older = this.metricsHistory.slice(-20, -10); // Previous 10 metrics

    if (older.length === 0) {
      return {
        performanceImprovement: 0,
        cacheEfficiencyTrend: 0,
        utilityOptimizationTrend: 0,
        errorRateTrend: 0,
      };
    }

    const recentAvg = {
      performance: recent.reduce((sum, m) => sum + m.averagePerformanceScore, 0) / recent.length,
      cacheHitRate: recent.reduce((sum, m) => sum + m.performance.cacheHitRate, 0) / recent.length,
      errorRate: recent.reduce((sum, m) => sum + m.performance.errorRate, 0) / recent.length,
    };

    const olderAvg = {
      performance: older.reduce((sum, m) => sum + m.averagePerformanceScore, 0) / older.length,
      cacheHitRate: older.reduce((sum, m) => sum + m.performance.cacheHitRate, 0) / older.length,
      errorRate: older.reduce((sum, m) => sum + m.performance.errorRate, 0) / older.length,
    };

    return {
      performanceImprovement:
        ((recentAvg.performance - olderAvg.performance) / olderAvg.performance) * 100,
      cacheEfficiencyTrend:
        ((recentAvg.cacheHitRate - olderAvg.cacheHitRate) / olderAvg.cacheHitRate) * 100,
      utilityOptimizationTrend: 0, // Placeholder
      errorRateTrend: ((recentAvg.errorRate - olderAvg.errorRate) / olderAvg.errorRate) * 100,
    };
  }

  /**
   * Get current dashboard metrics
   */
  getCurrentMetrics(): DashboardMetrics | null {
    return this.metricsHistory.length > 0
      ? this.metricsHistory[this.metricsHistory.length - 1]
      : null;
  }

  /**
   * Get metrics history
   */
  getMetricsHistory(hours: number = 24): DashboardMetrics[] {
    const cutoff = new Date(Date.now() - hours * 60 * 60 * 1000);
    return this.metricsHistory.filter((m) => m.timestamp >= cutoff);
  }

  /**
   * Get dashboard configuration
   */
  getConfig(): DashboardConfig {
    return { ...this.config };
  }

  /**
   * Update dashboard configuration
   */
  updateConfig(newConfig: Partial<DashboardConfig>): void {
    this.config = { ...this.config, ...newConfig };

    // Restart with new config if running
    if (this.isRunning) {
      this.stop();
      this.start();
    }

    logger.info('📊 Dashboard configuration updated');
    this.emit('configUpdated', this.config);
  }

  // Placeholder methods for calculations
  private getTotalScansToday(): number {
    return 0;
  }
  private calculateAveragePerformanceScore(): number {
    return 85;
  }
  private calculateAverageScanDuration(): number {
    return 30000;
  }
  private calculateErrorRate(): number {
    return 2;
  }
  private calculateAverageUtilityOverhead(): number {
    return 15;
  }
  private getMostUsedUtilities(): Array<{ utility: string; count: number }> {
    return [];
  }
  private getIntegrationStrategies(): Record<string, number> {
    return {};
  }
  private collectCurrentAlerts(): DashboardMetrics['alerts'] {
    return [];
  }
  private cleanOldMetrics(): void {
    const cutoff = new Date(Date.now() - this.config.metricsRetentionHours * 60 * 60 * 1000);
    this.metricsHistory = this.metricsHistory.filter((m) => m.timestamp >= cutoff);
  }
  private checkAlerts(metrics: DashboardMetrics): void {
    // Alert checking logic would go here
  }
}

export default RealTimeMonitoringDashboard;
